'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  MapPinIcon,
  ClockIcon,
  ChatBubbleLeftRightIcon,
  HeartIcon
} from '@heroicons/react/24/outline';

const contactMethods = [
  {
    icon: EnvelopeIcon,
    title: 'Email Us',
    description: 'Get in touch for any inquiries',
    contact: '<EMAIL>',
    action: 'mailto:<EMAIL>',
  },
  {
    icon: PhoneIcon,
    title: 'Call Us',
    description: 'Speak with our luxury consultants',
    contact: '+****************',
    action: 'tel:+15551234567',
  },
  {
    icon: MapPinIcon,
    title: 'Visit Us',
    description: 'Experience luxury in person',
    contact: '123 Luxury Avenue, Tech District',
    action: '#',
  },
];

const officeHours = [
  { day: 'Monday - Friday', hours: '9:00 AM - 6:00 PM' },
  { day: 'Saturday', hours: '10:00 AM - 4:00 PM' },
  { day: 'Sunday', hours: 'Closed' },
];

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    inquiryType: 'general'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const [focusedFields, setFocusedFields] = useState({
    name: false,
    email: false,
    subject: false,
    message: false,
  });

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleFocus = (field) => {
    setFocusedFields({
      ...focusedFields,
      [field]: true
    });
  };

  const handleBlur = (field) => {
    setFocusedFields({
      ...focusedFields,
      [field]: false
    });
  };

  const shouldLabelFloat = (field) => {
    return focusedFields[field] || formData[field].length > 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setSubmitted(true);
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: '',
      inquiryType: 'general'
    });
  };

  const colors = {
    backgroundPrimary: '#F8F8F8',
    backgroundSecondary: '#FFFFFF',
    textPrimary: '#0e2f2f',
    textSecondary: '#888888',
    accent: '#bfa980',
    accentLight: '#e9e5d6',
    border: '#E0E0E0',
    success: '#10B981',
  };

  const inputBaseStyle = {
    padding: "20px 0 10px 0",
    fontSize: "1em",
    borderRadius: "0",
    border: 'none',
    borderBottom: `1px solid ${colors.border}`,
    backgroundColor: 'transparent',
    color: colors.textPrimary,
    outline: "none",
    transition: "border-color 0.3s ease, border-bottom-width 0.3s ease",
    width: "100%",
    boxSizing: "border-box",
  };

  const inputFocusStyle = {
    borderColor: colors.accent,
    borderBottomWidth: '2px',
  };

  const labelBaseStyle = {
    position: "absolute",
    left: "0",
    top: "15px",
    color: colors.textSecondary,
    fontSize: "1em",
    pointerEvents: "none",
    transition: "top 0.3s ease, font-size 0.3s ease, color 0.3s ease, background-color 0.3s ease",
    zIndex: 1,
  };

  const labelFloatStyle = {
    top: "-10px",
    fontSize: "0.8em",
    color: colors.accent,
    backgroundColor: colors.backgroundSecondary,
    padding: "0 5px",
    left: "-5px",
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-[#0e2f2f] via-[#0e2f2f]/90 to-[#bfa980]/20" />
        
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-20 right-20 w-72 h-72 bg-[#bfa980] rounded-full blur-3xl" />
          <div className="absolute bottom-20 left-20 w-96 h-96 bg-[#e9e5d6] rounded-full blur-3xl" />
        </div>

        <div className="relative z-10 text-center px-6 max-w-4xl mx-auto mt-24">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <h1 className="text-5xl md:text-7xl font-serif font-extrabold text-white mb-6 tracking-wide">
              Get in
              <span className="block bg-gradient-to-r from-[#bfa980] to-[#e9e5d6] bg-clip-text text-transparent">
                Touch
              </span>
            </h1>
            <p className="text-xl md:text-2xl text-[#e9e5d6]/90 mb-8 leading-relaxed">
              We're here to help you discover the perfect luxury tech experience.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Contact Methods */}
      <section className="py-20 px-6 bg-white">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-serif font-bold text-[#0e2f2f] mb-6">
              Connect With Us
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose your preferred way to reach our luxury tech specialists.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {contactMethods.map((method, index) => (
              <motion.a
                key={index}
                href={method.action}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.2 }}
                viewport={{ once: true }}
                className="group bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-[#e9e5d6]/30 text-center block"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-[#bfa980] to-[#e9e5d6] rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <method.icon className="w-8 h-8 text-[#0e2f2f]" />
                </div>
                <h3 className="text-2xl font-serif font-bold text-[#0e2f2f] mb-3">
                  {method.title}
                </h3>
                <p className="text-gray-600 mb-4">
                  {method.description}
                </p>
                <p className="text-[#bfa980] font-semibold">
                  {method.contact}
                </p>
              </motion.a>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Office Hours */}
      <section className="py-20 px-6 bg-gradient-to-b from-gray-50 to-white">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-16">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <motion.div
                initial={{ opacity: 0, x: -50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
              >
                <h3 className="text-3xl font-serif font-bold text-[#0e2f2f] mb-8">
                  Send Us a Message
                </h3>
                
                {submitted ? (
                  <div className="bg-white p-8 rounded-2xl shadow-lg border border-[#e9e5d6]/30 text-center">
                    <div className="w-16 h-16 bg-gradient-to-r from-[#10B981] to-[#34D399] rounded-full flex items-center justify-center mx-auto mb-6">
                      <HeartIcon className="w-8 h-8 text-white" />
                    </div>
                    <h4 className="text-2xl font-serif font-bold text-[#0e2f2f] mb-4">
                      Thank You!
                    </h4>
                    <p className="text-gray-600 mb-6">
                      Your message has been sent successfully. We'll get back to you within 24 hours.
                    </p>
                    <button
                      onClick={() => setSubmitted(false)}
                      className="px-6 py-3 rounded-full bg-gradient-to-r from-[#bfa980] to-[#e9e5d6] text-[#0e2f2f] font-semibold hover:from-[#e9e5d6] hover:to-[#bfa980] transition-all"
                    >
                      Send Another Message
                    </button>
                  </div>
                ) : (
                  <form onSubmit={handleSubmit} className="bg-white p-8 rounded-2xl shadow-lg border border-[#e9e5d6]/30">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                      <div className="relative">
                        <label
                          htmlFor="name"
                          style={{
                            ...labelBaseStyle,
                            ...(shouldLabelFloat('name') ? labelFloatStyle : {}),
                          }}
                        >
                          Full Name
                        </label>
                        <input
                          id="name"
                          name="name"
                          type="text"
                          value={formData.name}
                          onChange={handleInputChange}
                          onFocus={() => handleFocus('name')}
                          onBlur={() => handleBlur('name')}
                          style={{
                            ...inputBaseStyle,
                            ...(focusedFields.name ? inputFocusStyle : {}),
                          }}
                          required
                        />
                      </div>

                      <div className="relative">
                        <label
                          htmlFor="email"
                          style={{
                            ...labelBaseStyle,
                            ...(shouldLabelFloat('email') ? labelFloatStyle : {}),
                          }}
                        >
                          Email Address
                        </label>
                        <input
                          id="email"
                          name="email"
                          type="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          onFocus={() => handleFocus('email')}
                          onBlur={() => handleBlur('email')}
                          style={{
                            ...inputBaseStyle,
                            ...(focusedFields.email ? inputFocusStyle : {}),
                          }}
                          required
                        />
                      </div>
                    </div>

                    <div className="mb-6">
                      <label className="block text-sm font-medium text-[#0e2f2f] mb-3">
                        Inquiry Type
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {['general', 'support', 'sales', 'partnership'].map((type) => (
                          <label key={type} className="flex items-center">
                            <input
                              type="radio"
                              name="inquiryType"
                              value={type}
                              checked={formData.inquiryType === type}
                              onChange={handleInputChange}
                              className="sr-only"
                            />
                            <div className={`px-4 py-2 rounded-full text-sm font-medium border transition-all cursor-pointer text-center w-full ${
                              formData.inquiryType === type
                                ? 'bg-[#bfa980] text-white border-[#bfa980]'
                                : 'text-[#0e2f2f] border-[#bfa980]/40 hover:bg-[#e9e5d6]/30'
                            }`}>
                              {type.charAt(0).toUpperCase() + type.slice(1)}
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>

                    <div className="relative mb-6">
                      <label
                        htmlFor="subject"
                        style={{
                          ...labelBaseStyle,
                          ...(shouldLabelFloat('subject') ? labelFloatStyle : {}),
                        }}
                      >
                        Subject
                      </label>
                      <input
                        id="subject"
                        name="subject"
                        type="text"
                        value={formData.subject}
                        onChange={handleInputChange}
                        onFocus={() => handleFocus('subject')}
                        onBlur={() => handleBlur('subject')}
                        style={{
                          ...inputBaseStyle,
                          ...(focusedFields.subject ? inputFocusStyle : {}),
                        }}
                        required
                      />
                    </div>

                    <div className="relative mb-8">
                      <label
                        htmlFor="message"
                        style={{
                          ...labelBaseStyle,
                          ...(shouldLabelFloat('message') ? labelFloatStyle : {}),
                        }}
                      >
                        Message
                      </label>
                      <textarea
                        id="message"
                        name="message"
                        rows="5"
                        value={formData.message}
                        onChange={handleInputChange}
                        onFocus={() => handleFocus('message')}
                        onBlur={() => handleBlur('message')}
                        style={{
                          ...inputBaseStyle,
                          ...(focusedFields.message ? inputFocusStyle : {}),
                          resize: 'vertical',
                          minHeight: '120px',
                        }}
                        required
                      />
                    </div>

                    <button
                      type="submit"
                      disabled={isSubmitting}
                      className={`w-full px-8 py-4 rounded-full font-semibold text-lg transition-all ${
                        isSubmitting
                          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                          : 'bg-gradient-to-r from-[#bfa980] to-[#e9e5d6] text-[#0e2f2f] hover:from-[#e9e5d6] hover:to-[#bfa980] transform hover:scale-105'
                      } shadow-lg`}
                    >
                      {isSubmitting ? 'Sending...' : 'Send Message'}
                    </button>
                  </form>
                )}
              </motion.div>
            </div>

            {/* Office Hours & Additional Info */}
            <div className="lg:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 50 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="space-y-8"
              >
                {/* Office Hours */}
                <div className="bg-white p-6 rounded-2xl shadow-lg border border-[#e9e5d6]/30">
                  <div className="flex items-center mb-4">
                    <ClockIcon className="w-6 h-6 text-[#bfa980] mr-3" />
                    <h4 className="text-xl font-serif font-bold text-[#0e2f2f]">
                      Office Hours
                    </h4>
                  </div>
                  <div className="space-y-3">
                    {officeHours.map((schedule, index) => (
                      <div key={index} className="flex justify-between">
                        <span className="text-gray-600">{schedule.day}</span>
                        <span className="text-[#0e2f2f] font-medium">{schedule.hours}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Quick Response */}
                <div className="bg-gradient-to-br from-[#0e2f2f] to-[#bfa980]/20 p-6 rounded-2xl text-white">
                  <div className="flex items-center mb-4">
                    <ChatBubbleLeftRightIcon className="w-6 h-6 text-[#e9e5d6] mr-3" />
                    <h4 className="text-xl font-serif font-bold">
                      Quick Response
                    </h4>
                  </div>
                  <p className="text-[#e9e5d6]/90 mb-4">
                    Need immediate assistance? Our luxury tech specialists are standing by.
                  </p>
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-block px-6 py-3 rounded-full bg-[#bfa980] text-[#0e2f2f] font-semibold hover:bg-[#e9e5d6] transition-all"
                  >
                    Email Support
                  </a>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
